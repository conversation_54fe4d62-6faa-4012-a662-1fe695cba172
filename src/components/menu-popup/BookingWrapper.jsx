'use client'
import { useEffect, useLayoutEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'
import Image from 'next/image'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import {createRoot} from 'react-dom/client';
import {APIProvider, Map} from '@vis.gl/react-google-maps';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import IslandAndExperiencesPopup from './PopupIslandAndExperiences'
import PopupIslandAndExperiences from './PopupIslandAndExperiences'
import PopupTestimonials from './PopupTestimonials'
import PagePopupLocationAndContact from './PagePopupLocationAndContact'

function PagePopup({item}) {
  const {image,body,title,secondaryEntries,url}=item
  const [dbData,setDbData]=useState([])

  const getData =async() => {
    fetch('/api/pages', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res=>res.json()).then(data=>setDbData(data))
  }
   
  useEffect(() => {
    getData()
  },[])

  console.log('BookingWrapper pagepopup:',dbData)

  return(
    <div className='popup-island/experiences flex flex-col mt-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
      <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
        <div className='flex uppercase font-thin w-full h-60 items-center justify-center border-[1px] border-gray-300 relative'>
          {/* <img src={data?.image} alt='page image' className='object-cover h-auto w-full'/> */}
          image goes here
        </div>
        <div className='flex w-full h-fit gap-10 flex-col lg:flex-row'>
          <div className='flex flex-col w-full h-fit gap-10'>
            <h1 className='w-full text-6xl text-left text-wrap leading-12'>
              Title goes here
            </h1>
            <div className='flex flex-col max-w-full md:max-w-[676px] gap-4'>
              <p className='text-left leading-7 text-3xl'>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt 
  laoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et.
              </p>
              <p className='text-left wfull font-thin leading-7'>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt 
  laoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et.  
  Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Cras vitae ante 
  sit amet nunc consequat feugiat sodales quis nisi. Integer sollicitudin quis turpis sed iaculis. 
              </p>
          </div>
          </div>
        </div>
        <div className='flex flex-col w-full h-fit mt-10 gap-20'>
          {[''].map((i,index)=>(
            <div key={index} className='flex flex-col lg:flex-row lg:even:flex-row-reverse w-full h-fit items-start justify-start gap-10'>
              <div className='flex uppercase font-thin h-full lg:h-[422px] w-full max-w-full lg:w-[474px] relative items-center justify-center border-[1px] border-gray-300 '>
                {/* <img src={i?.image} alt='page image' className='object-cover w-full h-auto'/> */}
                image goes here
              </div>
              <div className='flex max-w-full lg:w-[calc(100%-474px)] flex-col h-fit gap-5'>
                <h1 className='w-full  text-6xl text-left leading-12'>
                  Title goes here
                </h1>
                <div className='flex flex-col gap-4'>
                  <p className='w-full text-left leading-7 text-3xl gap-4'>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt 
laoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et.
                  </p>
                  <p className='w-full font-thin text-left leading-7'>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum quis nunc arcu. Mauris tincidunt 
laoreet arcu. Nullam lobortis commodo massa, id euismod diam viverra et.  
Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Cras vitae ante 
sit amet nunc consequat feugiat sodales quis nisi. Integer sollicitudin quis turpis sed iaculis.
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function PagePopupLocationAndContacts({item}) {
  const {image,body,title,secondaryEntries,url}=item
  const locationGPS={lat:-24.6424504,lng:25.8965668}
  const containerStyles={with:'100%',height:'100%'}
  const textList=[
    {title:'WHATSAPP',desc:'(+267) 72 808 308'},
    {title:'TELEPHONE',desc:'(+34) 72 808 308'},
    {title:'EMAIL',desc:'<EMAIL>'},
    {title:'ADDRESS',desc:`Boro River, Okavango,
            Maun, Botswana`
    },
    {title:'GPS CO-ORDINATES',desc:`19, 51'25, 2468 | S
            23, 26'7, 6056 | E`
    },
  ]
  const inputList=[
    {name:'name',type:'text'},
    {name:'email',type:'text'},
  ]
  
  console.log('BookingWrapper pagepopup:',process.env.GOOGLE_API_KEY)

  return(
    <div className='location-and-contacts-wrapper flex flex-col mt-[calc(75px+208px))] w-full items-center justify-start gap-5'>
      <div className='flex absolute top-[75px] uppercase font-thin w-full h-48 items-center justify-center overflow-hidden'>
        {/* <img src={image} alt='page image' className='object-cover h-auto w-full'/> */}
        <APIProvider apiKey={process.env.GOOGLE_API_KEY}>
          <Map
            style={{width: '100%', height: '100%'}}
            defaultCenter={{lat: locationGPS.lat, lng: locationGPS.lng}}
            defaultZoom={3}
            gestureHandling={'greedy'}
            disableDefaultUI={true}
          />
        </APIProvider>
      </div>
      <div className='flex max-w-full md:max-w-[676px] mt-2 h-full gap-10 flex-col md:flex-row'>
        <div className='flex w-1/3 flex-col'>
          <h1 className='w-full text-3xl leading-6 font-bold text-left text-wrap uppercase mb-2'>
            LOCATION & CONTACTS
          </h1>
          {textList?.map((i,index)=>
            <div className='flex flex-col gap-1' key={index}>
              <span className='font-bold uppercase'>{i?.title}</span>
              <span className='text-sm'>{i?.desc}</span>
            </div>
          )}
        </div>
        <div className='flex flex-col w-full'>
          <span>Do not hesitate to contact <span className='font-bold capitalize'>elephant island</span> by filling in the contact form below</span>
          <form className='flex mt-1 flex-col w-full gap-1' action="">
            {inputList?.map((i,index)=>
              <div key={index} className='flex flex-col w-full gap-1'>
                <span className='text-sm uppercase placeholder:text-white'>name</span>
                <input id={'name'} className='flex items-center h-10 gap-2 mt-0 border-6 border-white rounded-xl' placeholder='' type="text" />
            </div>)}
            <div className='flex flex-col w-full gap-1'>
              <span className='text-sm uppercase placeholder:text-white'>name</span>
              <textarea  minLength={500} rows={10} cols={5} id={'name'} className='flex items-center h-16 gap-2 mt-0 border-6 border-white rounded-xl' placeholder='' type="text" />
            </div>
            <input className='flex w-fit px-4 rounded-full h-7 bg-white text-gray-900 mt-4' type="button" value="send message" />
          </form>
        </div>
      </div>
    </div>
  )
}

function TestimonialsPopup({item}) {
  const {testimonials}=item
  // console.log('BookingWrapper pagepopup:',item)

  return(
    <div className='flex flex-col mt-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
      {testimonials?.map(i=>
        <div key={i?.name} className='flex w-full flex-col h-fit gap-5'>
          <h1 className='w-full text-4xl text-left leading-12 uppercase'>
            {i?.name}
          </h1>
          <p className='w-full text-left leading-7'>
            ''{i?.comment}''
          </p>
        </div>
      )}
    </div>
  )
}

export default function BookingWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const {experienceState,disptachExperience}=useContextExperience()
    const [pageInfo,setPageInfo]=useState([])
    const [closePopup,setClosePopup]=useState(false)
    const [showPages,setShowPages]=useState(false)
    // console.log('PopupWrapper:',query)

    const handleBookingClose=()=>{
      // console.log('booking close')
      disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
      // setClosePopup(true)
    }

    const handlePopupClose=()=>{
      {experienceState?.showTheIslandPage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_ISLAND_PAGE})}
      {experienceState?.showExperiencePage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_EXPERIENCE_PAGE})}
      {experienceState?.showTestimonials && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})}
      {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})}
    }

    const closeButton=()=>{
      console.log('close button click')
      setClosePopup(false)
      setShowPages(false)
    }    

    useEffect(() => {
      const fetchPageInfo = async () => {
        try {
          const res = await fetch('/api/pages', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (!res.ok) {
            throw new Error('Failed to fetch page info');
          }
          
          const data = await res.json();
          // console.log(data)
          setPageInfo(data?.data)
          return data;
        } catch (error) {
          console.error('Error fetching page info:', error);
        }
      };
      fetchPageInfo()
    }, [])
    
    // console.log('BookingWrapper:',pageInfo)

  return (
    // (closePopup &&
      <>
        {experienceState?.showBookingPopup && <div className='popup-wrapper flex z-10 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto'>
          <div 
            onClick={handleBookingClose} 
            className=" flex z-50 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
            >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>
          <div className='flex relative top-0 left0 h-fit px-5 md:w-[995px] mx-auto'>
            <BookingFormComponent/>
          </div>
        </div>}
        {experienceState?.showTheIslandPage && <PopupIslandAndExperiences data={pageInfo?.island}/>}
        {experienceState?.showExperiencePage && <PopupIslandAndExperiences data={pageInfo?.experiences}/>}
        {experienceState?.showTestimonials && <PopupTestimonials data={pageInfo?.testimonials}/>}
        {experienceState?.showLocationAndContacts && <PagePopupLocationAndContact data={pageInfo?.locationAndcontacts}/>}
      </>
    // )
  )
}