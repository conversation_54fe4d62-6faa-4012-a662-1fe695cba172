import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import React from 'react'

function HtmlContentDisplay({htmlString}) {
  return (
    <div dangerouslySetInnerHTML={{ __html: htmlString }} />
  );
}

export default function PopupTestimonials({data}) {
  const {experienceState,disptachExperience}=useContextExperience()
  const handlePopupClose=()=>{
    {experienceState?.showTestimonials && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})}
  }
  // console.log('PopupTestimonials:',data)
  return (
    <div className='popu-location-and-contactsflex z-30 absolute top-0 left-0 w-full h-full -bg-black/75 overflow-hidden overflow-y-auto'>
      <div className='popu-island-experiences flex z-30 absolute top-0 left-0 w-full h-full -bg-black/75 overflow-hidden overflow-y-auto text-white items-center justify-center'>
        <div className='popup-wrapper text-white flex z-10 absolute top-0 left-0 w-full h-full bg-black/85 overflow-y-auto'>
          <div 
            onClick={handlePopupClose} 
            className=" flex z-40 items-center justify-center absolute right-[104px] top-[0] h-[75px] w-[96px] cursor-pointer"
            >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>
          <div className='flex relative top-[75px] left-0 h-fit px-5 lg:w-[995px] mx-auto flex-col text-white mb-40'>
            <div>
              <HtmlContentDisplay htmlString={data?.body1}/>
            </div>
            <div className='flex w-full flex-col'>
              <h1 className='max-w-30 text-6xl leading-10 font-bold text-left text-wrap uppercase mb-2'>
                VISITOR TESTIMONIALS
              </h1>
              <p className='flex w-full text-sm'>
                What client's say...
              </p>
            </div>
            <div className='flex w-full h-fit flex-wrap'>
              {data?.testimonials?.map((i,index)=>(
                <div key={index} className='flex flex-col w-full md:max-w-1/2 lg:max-w-1/3 h-fit gap-4 p-4'>
                  <div className='flex w-14 h-14 rounded-full overflow-hidden'>
                    <img src="/assets/profile_pic_icon.png" alt="" />
                  </div>
                  <div className='w-full text-lg text-left leading-5 uppercase'>
                    <HtmlContentDisplay htmlString={i?.name}/>
                  </div>
                  <div className='w-full text-sm text-left leading-5'>
                    <HtmlContentDisplay htmlString={i?.comment}/>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
