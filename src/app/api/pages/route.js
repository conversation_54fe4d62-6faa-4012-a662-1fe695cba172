import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET - Fetch the single pages document with all 4 sections
export async function GET(request) {
  try {
    await connectDB();

    // Get the single pages document (there should only be one)
    let pages = await Page.findOne().lean();

    // If no pages document exists, initialize it
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne().lean();
    }

    return NextResponse.json({
      success: true,
      data: pages
    });

  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch pages', error: error.message },
      { status: 500 }
    );
  }
}

// POST - Update specific page section
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { section, data } = body;

    if (!section || !data) {
      return NextResponse.json(
        { success: false, message: 'Section and data are required' },
        { status: 400 }
      );
    }

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts'];
    if (!validSections.includes(section)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Run migration to clean up deprecated fields if needed
    // Check if any additional content items have body2 fields
    const needsMigration = (pages.island?.additionalContent?.some(item => item.body2 !== undefined)) ||
                          (pages.experiences?.additionalContent?.some(item => item.body2 !== undefined));

    if (needsMigration) {
      console.log('Running migration to remove deprecated body2 fields...');
      await Page.migrateDeprecatedFields();
      // Refresh the pages document after migration
      pages = await Page.findOne();
    }

    // Update the specific section
    pages[section] = data;
    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `${section} section updated successfully`,
      data: updatedPages
    });

  } catch (error) {
    console.error('Error updating page section:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page section', error: error.message },
      { status: 500 }
    );
  }
}

// PATCH - Update multiple sections at once
export async function PATCH(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Update multiple sections
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts'];
    let updatedSections = [];

    for (const [section, data] of Object.entries(body)) {
      if (validSections.includes(section)) {
        pages[section] = data;
        updatedSections.push(section);
      }
    }

    if (updatedSections.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid sections provided' },
        { status: 400 }
      );
    }

    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `Updated sections: ${updatedSections.join(', ')}`,
      data: updatedPages
    });

  } catch (error) {
    console.error('Error in bulk update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk update', error: error.message },
      { status: 500 }
    );
  }
}

// DELETE - Reset all pages to default
export async function DELETE(request) {
  try {
    await connectDB();

    // Delete the existing pages document
    await Page.deleteMany({});

    // Reinitialize with defaults
    await Page.initializeDefaultPages();
    const newPages = await Page.findOne();

    return NextResponse.json({
      success: true,
      message: 'All pages reset to default values',
      data: newPages
    });

  } catch (error) {
    console.error('Error resetting pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to reset pages', error: error.message },
      { status: 500 }
    );
  }
}
